"""
Setup Views - API views for company setup and configuration
Following DRF best practices with comprehensive setup functionality
"""
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from datetime import datetime, date
import logging

from .models import (
    Country, Currency, Timezone, CompanySetup, BankAccount,
    TaxConfiguration, WithholdingTaxConfiguration, PaymentTerm, SetupWizardStep, SystemConfiguration,
    SetupTemplate
)
from .serializers import (
    CountrySerializer, CurrencySerializer, TimezoneSerializer,
    CompanySetupSerializer, CompanySetupSummarySerializer,
    BankAccountSerializer, TaxConfigurationSerializer, WithholdingTaxConfigurationSerializer,
    PaymentTermSerializer, SetupWizardStepSerializer,
    SystemConfigurationSerializer, SetupTemplateSerializer,
    SetupProgressSerializer, SetupValidationSerializer
)

logger = logging.getLogger(__name__)

class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """Country viewset for localization data"""
    queryset = Country.objects.filter(is_active=True)
    serializer_class = CountrySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)
        return queryset.order_by('name')

class CurrencyViewSet(viewsets.ReadOnlyModelViewSet):
    """Currency viewset for multi-currency support"""
    queryset = Currency.objects.filter(is_active=True)
    serializer_class = CurrencySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)
        return queryset.order_by('name')

class TimezoneViewSet(viewsets.ReadOnlyModelViewSet):
    """Timezone viewset for localization"""
    queryset = Timezone.objects.filter(is_active=True)
    serializer_class = TimezoneSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return super().get_queryset().order_by('offset', 'name')

class CompanySetupViewSet(viewsets.ModelViewSet):
    """Company setup viewset with comprehensive setup functionality"""
    queryset = CompanySetup.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'list':
            return CompanySetupSummarySerializer
        return CompanySetupSerializer

    def get_queryset(self):
        # Users can only see their own company setups
        return super().get_queryset().filter(created_by=self.request.user)

    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """Get setup progress for a company"""
        company = self.get_object()

        # Define setup steps
        setup_steps = [
            {'name': 'company', 'title': 'Company Information', 'order': 1},
            {'name': 'localization', 'title': 'Localization', 'order': 2},
            {'name': 'fiscal', 'title': 'Fiscal Settings', 'order': 3},
            {'name': 'banking', 'title': 'Banking', 'order': 4},
            {'name': 'taxes', 'title': 'Tax Configuration', 'order': 5},
        ]

        # Get completed steps
        completed_steps = SetupWizardStep.objects.filter(
            company=company,
            is_completed=True
        ).count()

        # Calculate progress
        progress_data = {
            'total_steps': len(setup_steps),
            'completed_steps': completed_steps,
            'current_step': company.setup_step,
            'progress_percentage': company.setup_progress,
            'next_step': self._get_next_step(company.setup_step, setup_steps),
            'is_completed': company.setup_completed,
            'steps': setup_steps
        }

        serializer = SetupProgressSerializer(progress_data)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def complete_step(self, request, pk=None):
        """Mark a setup step as completed"""
        company = self.get_object()
        step_name = request.data.get('step_name')
        step_data = request.data.get('data', {})

        if not step_name:
            return Response(
                {'error': 'step_name is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Update or create wizard step
                wizard_step, created = SetupWizardStep.objects.get_or_create(
                    company=company,
                    step_name=step_name,
                    defaults={
                        'step_order': self._get_step_order(step_name),
                        'data': step_data
                    }
                )

                wizard_step.is_completed = True
                wizard_step.completed_at = timezone.now()
                wizard_step.data = step_data
                wizard_step.save()

                # Update company setup step
                company.setup_step = self._get_next_step(step_name)
                company.save()

                return Response({
                    'message': f'Step {step_name} completed successfully',
                    'next_step': company.setup_step,
                    'progress': company.setup_progress
                })

        except Exception as e:
            logger.error(f"Error completing setup step: {e}")
            return Response(
                {'error': 'Failed to complete setup step'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def validate(self, request, pk=None):
        """Validate company setup completeness"""
        company = self.get_object()

        errors = []
        warnings = []
        missing_fields = []

        # Required fields validation
        required_fields = {
            'name': 'Company name',
            'email': 'Email address',
            'phone': 'Phone number',
            'street': 'Street address',
            'city': 'City',
            'country': 'Country',
            'currency': 'Currency',
            'timezone': 'Timezone',
            'fiscal_year_start': 'Fiscal year start date'
        }

        for field, label in required_fields.items():
            if not getattr(company, field):
                missing_fields.append(label)
                errors.append(f'{label} is required')

        # Business logic validations
        if company.fiscal_year_start and company.fiscal_year_end:
            if company.fiscal_year_start >= company.fiscal_year_end:
                errors.append('Fiscal year start date must be before end date')

        # Check for bank accounts
        if not company.bank_accounts.filter(is_active=True).exists():
            warnings.append('No bank accounts configured')

        # Check for tax configurations
        if not company.tax_configurations.filter(is_active=True).exists():
            warnings.append('No tax configurations set up')

        validation_data = {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'missing_fields': missing_fields
        }

        serializer = SetupValidationSerializer(validation_data)
        return Response(serializer.data)

    def _get_step_order(self, step_name):
        """Get order number for setup step"""
        step_orders = {
            'company': 1,
            'localization': 2,
            'fiscal': 3,
            'banking': 4,
            'taxes': 5
        }
        return step_orders.get(step_name, 99)

    def _get_next_step(self, current_step, steps=None):
        """Get next setup step"""
        step_sequence = ['company', 'localization', 'fiscal', 'banking', 'taxes', 'completed']

        try:
            current_index = step_sequence.index(current_step)
            if current_index < len(step_sequence) - 1:
                return step_sequence[current_index + 1]
        except ValueError:
            pass

        return 'completed'

class BankAccountViewSet(viewsets.ModelViewSet):
    """Bank account viewset for company banking setup"""
    serializer_class = BankAccountSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        company_id = self.request.query_params.get('company')
        if company_id:
            return BankAccount.objects.filter(
                company_id=company_id,
                is_active=True
            ).order_by('-is_default', 'bank_name')
        return BankAccount.objects.none()

class TaxConfigurationViewSet(viewsets.ModelViewSet):
    """Tax configuration viewset for company tax setup"""
    serializer_class = TaxConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # For detail views (retrieve, update, delete), return all records
        if self.action in ['retrieve', 'update', 'partial_update', 'destroy']:
            return TaxConfiguration.objects.all()

        # For list views, filter by company
        company_id = self.request.query_params.get('company')
        if company_id:
            return TaxConfiguration.objects.filter(
                company=company_id,
                is_active=True
            ).order_by('-is_default', 'name')
        return TaxConfiguration.objects.none()


class WithholdingTaxConfigurationViewSet(viewsets.ModelViewSet):
    """Withholding tax configuration viewset for TDS setup"""
    serializer_class = WithholdingTaxConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # For detail views (retrieve, update, delete), return all records
        if self.action in ['retrieve', 'update', 'partial_update', 'destroy']:
            return WithholdingTaxConfiguration.objects.all()

        # For list views, filter by company
        company_id = self.request.query_params.get('company')
        if company_id:
            return WithholdingTaxConfiguration.objects.filter(
                company_id=company_id,
                is_active=True
            ).order_by('tax_type', 'name')
        return WithholdingTaxConfiguration.objects.none()

    @action(detail=True, methods=['post'])
    def calculate_withholding_tax(self, request, pk=None):
        """Calculate withholding tax for given payment amount"""
        withholding_tax = self.get_object()
        payment_amount = request.data.get('payment_amount', 0)
        cumulative_amount = request.data.get('cumulative_amount', 0)

        try:
            payment_amount = float(payment_amount)
            cumulative_amount = float(cumulative_amount)
            tax_amount = withholding_tax.calculate_withholding_tax(payment_amount, cumulative_amount)

            return Response({
                'payment_amount': payment_amount,
                'cumulative_amount': cumulative_amount,
                'withholding_tax_amount': tax_amount,
                'net_payment': payment_amount - tax_amount,
                'effective_rate': (tax_amount / payment_amount * 100) if payment_amount > 0 else 0,
                'is_applicable': withholding_tax.is_applicable_for_amount(payment_amount),
                'next_payment_due': withholding_tax.get_next_payment_due_date(),
                'tax_configuration': self.get_serializer(withholding_tax).data
            })
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid payment amount provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def by_tax_type(self, request):
        """Get withholding tax configurations grouped by tax type"""
        company_id = request.query_params.get('company')
        if not company_id:
            return Response({'error': 'Company ID required'}, status=status.HTTP_400_BAD_REQUEST)

        configurations = WithholdingTaxConfiguration.objects.filter(
            company_id=company_id,
            is_active=True
        ).order_by('tax_type', 'name')

        # Group by tax type
        grouped_data = {}
        for config in configurations:
            tax_type = config.get_tax_type_display()
            if tax_type not in grouped_data:
                grouped_data[tax_type] = []
            grouped_data[tax_type].append(self.get_serializer(config).data)

        return Response(grouped_data)

class PaymentTermViewSet(viewsets.ModelViewSet):
    """Payment term viewset for company payment terms setup"""
    serializer_class = PaymentTermSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        company_id = self.request.query_params.get('company')
        if company_id:
            return PaymentTerm.objects.filter(
                company_id=company_id,
                is_active=True
            ).order_by('days')
        return PaymentTerm.objects.none()

class SetupTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """Setup template viewset for predefined configurations"""
    queryset = SetupTemplate.objects.filter(is_active=True)
    serializer_class = SetupTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        business_type = self.request.query_params.get('business_type')
        queryset = super().get_queryset()

        if business_type:
            queryset = queryset.filter(business_type=business_type)

        return queryset.order_by('name')

    @action(detail=True, methods=['post'])
    def apply_template(self, request, pk=None):
        """Apply setup template to a company"""
        template = self.get_object()
        company_id = request.data.get('company_id')

        if not company_id:
            return Response(
                {'error': 'company_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            company = CompanySetup.objects.get(id=company_id, created_by=request.user)
        except CompanySetup.DoesNotExist:
            return Response(
                {'error': 'Company not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            with transaction.atomic():
                # Apply template data to company
                template_data = template.template_data

                # Update company fields from template
                for field, value in template_data.get('company', {}).items():
                    if hasattr(company, field):
                        setattr(company, field, value)

                company.save()

                # Create tax configurations from template
                for tax_config in template_data.get('taxes', []):
                    TaxConfiguration.objects.create(
                        company=company,
                        **tax_config
                    )

                # Create payment terms from template
                for payment_term in template_data.get('payment_terms', []):
                    PaymentTerm.objects.create(
                        company=company,
                        **payment_term
                    )

                return Response({
                    'message': f'Template {template.name} applied successfully',
                    'company_id': company.id
                })

        except Exception as e:
            logger.error(f"Error applying template: {e}")
            return Response(
                {'error': 'Failed to apply template'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class SystemConfigurationViewSet(viewsets.ModelViewSet):
    """System configuration viewset for admin settings"""
    queryset = SystemConfiguration.objects.filter(is_active=True)
    serializer_class = SystemConfigurationSerializer
    permission_classes = [permissions.IsAdminUser]  # Only admin users

    def get_queryset(self):
        key = self.request.query_params.get('key')
        queryset = super().get_queryset()

        if key:
            queryset = queryset.filter(key__icontains=key)

        return queryset.order_by('key')
