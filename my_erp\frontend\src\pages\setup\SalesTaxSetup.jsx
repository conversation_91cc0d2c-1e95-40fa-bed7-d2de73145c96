/**
 * Sales Tax Setup Page - Manage sales tax configurations
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm, Tag } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { PageHeader, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import SalesTaxForm from './components/SalesTaxForm';

const SalesTaxSetup = () => {
  const [salesTaxes, setSalesTaxes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTax, setEditingTax] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  const [selectedCompany] = useState(1); // TODO: Get from context

  // Fetch sales tax configurations
  const fetchSalesTaxes = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getTaxConfigurations(selectedCompany);
      setSalesTaxes(response.data?.results || response.data || []);
    } catch (error) {
      message.error('Failed to fetch sales tax configurations');
      console.error('Fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSalesTaxes();
  }, [selectedCompany]);

  // Handle create
  const handleCreate = () => {
    setEditingTax(null);
    setModalVisible(true);
  };

  // Handle edit
  const handleEdit = (tax) => {
    console.log('=== EDITING SALES TAX ===');
    console.log('Tax data:', JSON.stringify(tax, null, 2));
    setEditingTax(tax);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = async (id) => {
    try {
      await setupAPI.deleteTaxConfiguration(id);
      message.success('Sales tax configuration deleted successfully');
      fetchSalesTaxes();
    } catch (error) {
      message.error('Failed to delete sales tax configuration');
      console.error('Delete error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      const taxData = {
        ...values,
        company: selectedCompany
      };

      console.log('=== SALES TAX FORM DATA BEING SENT ===');
      console.log(JSON.stringify(taxData, null, 2));

      if (editingTax) {
        await setupAPI.updateTaxConfiguration(editingTax.id, taxData);
        message.success('Sales tax configuration updated successfully');
      } else {
        await setupAPI.createTaxConfiguration(taxData);
        message.success('Sales tax configuration created successfully');
      }
      setModalVisible(false);
      setEditingTax(null);
      fetchSalesTaxes();
    } catch (error) {
      message.error(`Failed to ${editingTax ? 'update' : 'create'} sales tax configuration`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Table columns
  const columns = [
    {
      title: 'Tax Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Rate (%)',
      dataIndex: 'rate',
      key: 'rate',
      render: (rate) => `${rate}%`,
      sorter: (a, b) => a.rate - b.rate,
    },
    {
      title: 'Tax Type',
      dataIndex: 'tax_type',
      key: 'tax_type',
      render: (type) => (
        <Tag color={type === 'sales' ? 'green' : type === 'purchase' ? 'blue' : 'purple'}>
          {type?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (is_active, record) => (
        <Space direction="vertical" size={2}>
          <Tag color={is_active ? 'green' : 'red'}>
            {is_active ? 'Active' : 'Inactive'}
          </Tag>
          {record.is_default && (
            <Tag color="gold">Default</Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <QuickActions.Edit
            onClick={() => handleEdit(record)}
            disabled={loading}
          />
          <Popconfirm
            title="Are you sure you want to delete this sales tax?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <QuickActions.Delete disabled={loading} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Sales Tax Setup' }
  ];

  const actions = [
    {
      label: 'New Sales Tax',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreate
    }
  ];

  return (
    <>
      <PageHeader
        title="Sales Tax Setup"
        subtitle="Configure sales tax rates and purchase tax rates for your company"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />

      <PageContent>
        <Table
          columns={columns}
          dataSource={salesTaxes}
          loading={loading}
          rowKey="id"
          pagination={{
            total: salesTaxes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} sales taxes`,
          }}
          scroll={{ x: 1000 }}
        />
      </PageContent>

      <Modal
        title={editingTax ? 'Edit Sales Tax' : 'Create New Sales Tax'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnHidden
      >
        <SalesTaxForm
          initialValues={editingTax}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default SalesTaxSetup;
