# Generated manually for Chart of Accounts enhancements

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0005_delete_rescountry'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='accountaccount',
            name='currency_id',
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='opening_balance',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Opening balance for this account', max_digits=16),
        ),
        migrations.AddField(
            model_name='accountaccount',
            name='parent_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_accounts', to='accounting.accountaccount'),
        ),
    ]
