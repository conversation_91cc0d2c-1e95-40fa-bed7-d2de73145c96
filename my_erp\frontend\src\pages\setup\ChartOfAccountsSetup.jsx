/**
 * Chart of Accounts Setup Page - Manage hierarchical chart of accounts
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm, Tag, Tree, Tabs, Button } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined, FileOutlined } from '@ant-design/icons';
import { PageHeader, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { accountingAPI } from '../../services/api';
import ChartOfAccountsForm from './components/ChartOfAccountsForm';

const ChartOfAccountsSetup = () => {
  const [accounts, setAccounts] = useState([]);
  const [accountGroups, setAccountGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('hierarchy');
  const [modalType, setModalType] = useState('account'); // 'account' or 'group'

  const [selectedCompany] = useState(1); // TODO: Get from context

  // Account types with colors for consistent display
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', color: 'blue' },
    { value: 'asset_cash', label: 'Bank and Cash', color: 'green' },
    { value: 'asset_current', label: 'Current Assets', color: 'cyan' },
    { value: 'asset_non_current', label: 'Non-current Assets', color: 'geekblue' },
    { value: 'asset_fixed', label: 'Fixed Assets', color: 'purple' },
    { value: 'liability_payable', label: 'Payable', color: 'red' },
    { value: 'liability_current', label: 'Current Liabilities', color: 'volcano' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', color: 'magenta' },
    { value: 'equity', label: 'Equity', color: 'gold' },
    { value: 'income', label: 'Income', color: 'lime' },
    { value: 'expense', label: 'Expenses', color: 'orange' },
  ];

  // Fetch accounts only (groups temporarily disabled)
  const fetchData = async () => {
    setLoading(true);
    try {
      const accountsResponse = await accountingAPI.getAccounts();
      setAccounts(accountsResponse.data?.results || accountsResponse.data || []);
      setAccountGroups([]); // Temporarily empty
    } catch (error) {
      message.error('Failed to fetch chart of accounts data');
      console.error('Fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [selectedCompany]);

  // Handle create account
  const handleCreateAccount = () => {
    setEditingItem(null);
    setModalType('account');
    setModalVisible(true);
  };

  // Handle create group (temporarily disabled)
  const handleCreateGroup = () => {
    message.info('Account groups feature coming soon!');
    // setEditingItem(null);
    // setModalType('group');
    // setModalVisible(true);
  };

  // Handle edit
  const handleEdit = (item, type) => {
    console.log('=== EDITING ITEM ===');
    console.log('Item data:', JSON.stringify(item, null, 2));
    console.log('Type:', type);
    setEditingItem(item);
    setModalType(type);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = async (id, type) => {
    try {
      if (type === 'account') {
        await accountingAPI.deleteAccount(id);
        message.success('Account deleted successfully');
      } else {
        message.error('Account groups not yet implemented');
        return;
      }
      fetchData();
    } catch (error) {
      message.error(`Failed to delete ${type}`);
      console.error('Delete error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      const data = {
        ...values,
        company_id: selectedCompany
      };

      console.log('=== FORM DATA BEING SENT ===');
      console.log(JSON.stringify(data, null, 2));

      if (editingItem) {
        if (modalType === 'account') {
          await accountingAPI.updateAccount(editingItem.id, data);
          message.success('Account updated successfully');
        } else {
          message.error('Account groups not yet implemented');
          return;
        }
      } else {
        if (modalType === 'account') {
          await accountingAPI.createAccount(data);
          message.success('Account created successfully');
        } else {
          message.error('Account groups not yet implemented');
          return;
        }
      }
      setModalVisible(false);
      setEditingItem(null);
      fetchData();
    } catch (error) {
      message.error(`Failed to ${editingItem ? 'update' : 'create'} ${modalType}`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Build tree data for hierarchy view (accounts only for now)
  const buildTreeData = () => {
    const treeData = [];

    const buildAccountNode = (account) => {
      const children = [];

      // Add child accounts
      const childAccounts = accounts.filter(acc => acc.parent_id === account.id);
      childAccounts.forEach(childAccount => {
        children.push(buildAccountNode(childAccount));
      });

      return {
        key: `account-${account.id}`,
        title: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>
              <FileOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              {account.code} - {account.name}
              <Tag color={accountTypes.find(t => t.value === account.account_type)?.color || 'default'} style={{ marginLeft: 8 }}>
                {accountTypes.find(t => t.value === account.account_type)?.label || account.account_type}
              </Tag>
              {account.opening_balance && account.opening_balance !== '0.00' && (
                <Tag color="gold" style={{ marginLeft: 8 }}>
                  Opening: ${account.opening_balance}
                </Tag>
              )}
            </span>
            <Space>
              <QuickActions.Edit
                onClick={() => handleEdit(account, 'account')}
                disabled={loading}
              />
              <Popconfirm
                title="Are you sure you want to delete this account?"
                onConfirm={() => handleDelete(account.id, 'account')}
                okText="Yes"
                cancelText="No"
              >
                <QuickActions.Delete disabled={loading} />
              </Popconfirm>
            </Space>
          </div>
        ),
        children: children.length > 0 ? children : undefined,
        isLeaf: children.length === 0,
        data: account,
        type: 'account'
      };
    };

    // Add root accounts (accounts without parent)
    const rootAccounts = accounts.filter(acc => !acc.parent_id);
    rootAccounts.forEach(account => {
      treeData.push(buildAccountNode(account));
    });

    return treeData;
  };

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Chart of Accounts' }
  ];

  const actions = [
    {
      label: 'New Account Group',
      type: 'default',
      icon: <FolderOutlined />,
      onClick: handleCreateGroup
    },
    {
      label: 'New Account',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreateAccount
    }
  ];

  return (
    <>
      <PageHeader
        title="Chart of Accounts Setup"
        subtitle="Manage your chart of accounts structure with hierarchical organization"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />
      
      <PageContent>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'hierarchy',
              label: (
                <span>
                  <FolderOutlined />
                  Hierarchy View
                </span>
              ),
              children: (
                <div style={{ padding: '16px 0' }}>
                  <Tree
                    showLine
                    showIcon={false}
                    defaultExpandAll
                    treeData={buildTreeData()}
                    style={{ background: '#fff', padding: '16px', borderRadius: '6px' }}
                  />
                </div>
              )
            }
          ]}
        />
      </PageContent>

      <Modal
        title={`${editingItem ? 'Edit' : 'Create New'} ${modalType === 'account' ? 'Account' : 'Account Group'}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnHidden
      >
        <ChartOfAccountsForm
          initialValues={editingItem}
          type={modalType}
          accountTypes={accountTypes}
          accountGroups={accountGroups}
          accounts={accounts}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default ChartOfAccountsSetup;
