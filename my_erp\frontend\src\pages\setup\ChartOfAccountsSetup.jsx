/**
 * Chart of Accounts Setup Page - Manage hierarchical chart of accounts
 */
import React, { useState, useEffect } from 'react';
import { Table, Space, Modal, message, Popconfirm, Tag, Tree, Tabs, Button } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined, FileOutlined } from '@ant-design/icons';
import { PageHeader, PageContent, QuickActions } from '../../components/shared/layout/PageLayout';
import { accountingAPI } from '../../services/api';
import ChartOfAccountsForm from './components/ChartOfAccountsForm';

const ChartOfAccountsSetup = () => {
  const [accounts, setAccounts] = useState([]);
  const [accountGroups, setAccountGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('hierarchy');
  const [modalType, setModalType] = useState('account'); // 'account' or 'group'

  const [selectedCompany] = useState(1); // TODO: Get from context

  // Account types with colors for consistent display
  const accountTypes = [
    { value: 'asset_receivable', label: 'Receivable', color: 'blue' },
    { value: 'asset_cash', label: 'Bank and Cash', color: 'green' },
    { value: 'asset_current', label: 'Current Assets', color: 'cyan' },
    { value: 'asset_non_current', label: 'Non-current Assets', color: 'geekblue' },
    { value: 'asset_fixed', label: 'Fixed Assets', color: 'purple' },
    { value: 'liability_payable', label: 'Payable', color: 'red' },
    { value: 'liability_current', label: 'Current Liabilities', color: 'volcano' },
    { value: 'liability_non_current', label: 'Non-current Liabilities', color: 'magenta' },
    { value: 'equity', label: 'Equity', color: 'gold' },
    { value: 'income', label: 'Income', color: 'lime' },
    { value: 'expense', label: 'Expenses', color: 'orange' },
  ];

  // Fetch accounts and groups
  const fetchData = async () => {
    setLoading(true);
    try {
      const [accountsResponse, groupsResponse] = await Promise.all([
        accountingAPI.getAccounts(),
        accountingAPI.getAccountGroups()
      ]);
      setAccounts(accountsResponse.data?.results || accountsResponse.data || []);
      setAccountGroups(groupsResponse.data?.results || groupsResponse.data || []);
    } catch (error) {
      message.error('Failed to fetch chart of accounts data');
      console.error('Fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [selectedCompany]);

  // Handle create account
  const handleCreateAccount = () => {
    setEditingItem(null);
    setModalType('account');
    setModalVisible(true);
  };

  // Handle create group
  const handleCreateGroup = () => {
    setEditingItem(null);
    setModalType('group');
    setModalVisible(true);
  };

  // Handle edit
  const handleEdit = (item, type) => {
    console.log('=== EDITING ITEM ===');
    console.log('Item data:', JSON.stringify(item, null, 2));
    console.log('Type:', type);
    setEditingItem(item);
    setModalType(type);
    setModalVisible(true);
  };

  // Handle delete
  const handleDelete = async (id, type) => {
    try {
      if (type === 'account') {
        await accountingAPI.deleteAccount(id);
        message.success('Account deleted successfully');
      } else {
        await accountingAPI.deleteAccountGroup(id);
        message.success('Account group deleted successfully');
      }
      fetchData();
    } catch (error) {
      message.error(`Failed to delete ${type}`);
      console.error('Delete error:', error);
    }
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      const data = {
        ...values,
        company_id: selectedCompany
      };

      console.log('=== FORM DATA BEING SENT ===');
      console.log(JSON.stringify(data, null, 2));

      if (editingItem) {
        if (modalType === 'account') {
          await accountingAPI.updateAccount(editingItem.id, data);
          message.success('Account updated successfully');
        } else {
          await accountingAPI.updateAccountGroup(editingItem.id, data);
          message.success('Account group updated successfully');
        }
      } else {
        if (modalType === 'account') {
          await accountingAPI.createAccount(data);
          message.success('Account created successfully');
        } else {
          await accountingAPI.createAccountGroup(data);
          message.success('Account group created successfully');
        }
      }
      setModalVisible(false);
      setEditingItem(null);
      fetchData();
    } catch (error) {
      message.error(`Failed to ${editingItem ? 'update' : 'create'} ${modalType}`);
      console.error('Form submit error:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // Build tree data for hierarchy view
  const buildTreeData = () => {
    const treeData = [];
    
    // Add root groups first
    const rootGroups = accountGroups.filter(group => !group.parent_id);
    
    const buildGroupNode = (group) => {
      const children = [];
      
      // Add child groups
      const childGroups = accountGroups.filter(g => g.parent_id === group.id);
      childGroups.forEach(childGroup => {
        children.push(buildGroupNode(childGroup));
      });
      
      // Add accounts in this group
      const groupAccounts = accounts.filter(acc => acc.group_id === group.id);
      groupAccounts.forEach(account => {
        children.push({
          key: `account-${account.id}`,
          title: (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>
                <FileOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                {account.code} - {account.name}
                <Tag color={accountTypes.find(t => t.value === account.account_type)?.color || 'default'} style={{ marginLeft: 8 }}>
                  {accountTypes.find(t => t.value === account.account_type)?.label || account.account_type}
                </Tag>
              </span>
              <Space>
                <QuickActions.Edit
                  onClick={() => handleEdit(account, 'account')}
                  disabled={loading}
                />
                <Popconfirm
                  title="Are you sure you want to delete this account?"
                  onConfirm={() => handleDelete(account.id, 'account')}
                  okText="Yes"
                  cancelText="No"
                >
                  <QuickActions.Delete disabled={loading} />
                </Popconfirm>
              </Space>
            </div>
          ),
          isLeaf: true,
          data: account,
          type: 'account'
        });
      });
      
      return {
        key: `group-${group.id}`,
        title: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>
              <FolderOutlined style={{ marginRight: 8, color: '#faad14' }} />
              {group.name}
              <Tag color="processing" style={{ marginLeft: 8 }}>
                {group.accounts_count || 0} accounts
              </Tag>
            </span>
            <Space>
              <QuickActions.Edit
                onClick={() => handleEdit(group, 'group')}
                disabled={loading}
              />
              <Popconfirm
                title="Are you sure you want to delete this group?"
                onConfirm={() => handleDelete(group.id, 'group')}
                okText="Yes"
                cancelText="No"
              >
                <QuickActions.Delete disabled={loading} />
              </Popconfirm>
            </Space>
          </div>
        ),
        children: children.length > 0 ? children : undefined,
        data: group,
        type: 'group'
      };
    };
    
    rootGroups.forEach(group => {
      treeData.push(buildGroupNode(group));
    });
    
    // Add ungrouped accounts
    const ungroupedAccounts = accounts.filter(acc => !acc.group_id);
    if (ungroupedAccounts.length > 0) {
      const ungroupedNode = {
        key: 'ungrouped',
        title: (
          <span>
            <FolderOutlined style={{ marginRight: 8, color: '#d9d9d9' }} />
            Ungrouped Accounts
          </span>
        ),
        children: ungroupedAccounts.map(account => ({
          key: `account-${account.id}`,
          title: (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>
                <FileOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                {account.code} - {account.name}
                <Tag color={accountTypes.find(t => t.value === account.account_type)?.color || 'default'} style={{ marginLeft: 8 }}>
                  {accountTypes.find(t => t.value === account.account_type)?.label || account.account_type}
                </Tag>
              </span>
              <Space>
                <QuickActions.Edit
                  onClick={() => handleEdit(account, 'account')}
                  disabled={loading}
                />
                <Popconfirm
                  title="Are you sure you want to delete this account?"
                  onConfirm={() => handleDelete(account.id, 'account')}
                  okText="Yes"
                  cancelText="No"
                >
                  <QuickActions.Delete disabled={loading} />
                </Popconfirm>
              </Space>
            </div>
          ),
          isLeaf: true,
          data: account,
          type: 'account'
        }))
      };
      treeData.push(ungroupedNode);
    }
    
    return treeData;
  };

  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Chart of Accounts' }
  ];

  const actions = [
    {
      label: 'New Account Group',
      type: 'default',
      icon: <FolderOutlined />,
      onClick: handleCreateGroup
    },
    {
      label: 'New Account',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreateAccount
    }
  ];

  return (
    <>
      <PageHeader
        title="Chart of Accounts Setup"
        subtitle="Manage your chart of accounts structure with hierarchical organization"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />
      
      <PageContent>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'hierarchy',
              label: (
                <span>
                  <FolderOutlined />
                  Hierarchy View
                </span>
              ),
              children: (
                <div style={{ padding: '16px 0' }}>
                  <Tree
                    showLine
                    showIcon={false}
                    defaultExpandAll
                    treeData={buildTreeData()}
                    style={{ background: '#fff', padding: '16px', borderRadius: '6px' }}
                  />
                </div>
              )
            }
          ]}
        />
      </PageContent>

      <Modal
        title={`${editingItem ? 'Edit' : 'Create New'} ${modalType === 'account' ? 'Account' : 'Account Group'}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnClose
      >
        <ChartOfAccountsForm
          initialValues={editingItem}
          type={modalType}
          accountTypes={accountTypes}
          accountGroups={accountGroups}
          accounts={accounts}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default ChartOfAccountsSetup;
