/**
 * Company Setup Page - Odoo-style company management
 * Following Odoo's res.company structure and UI patterns
 */
import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  message,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { PageHeader, PageContent } from '../../components/shared/layout/PageLayout';
import { setupAPI } from '../../services/api';
import CompanyForm from './components/CompanyForm';

const CompanySetup = () => {
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState(null);
  const [formLoading, setFormLoading] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);

  // Fetch companies
  const fetchCompanies = async () => {
    setLoading(true);
    try {
      const response = await setupAPI.getCompanies();
      // Handle Django REST Framework paginated response
      const companiesData = response.data?.results || response.data || [];
      setCompanies(Array.isArray(companiesData) ? companiesData : []);
    } catch (error) {
      message.error('Failed to fetch companies');
      console.error('Fetch companies error:', error);
      setCompanies([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  // Handle create company
  const handleCreate = () => {
    setEditingCompany(null);
    setModalVisible(true);
  };

  // Handle edit company (same as configure for Odoo-style)
  const handleEdit = (company) => {
    setEditingCompany(company);
    setModalVisible(true);
  };

  // Handle form submit
  const handleFormSubmit = async (values) => {
    setFormLoading(true);
    try {
      // Simple data - just send what we have
      const companyData = {
        name: values.name || 'New Company',
        email: values.email || '',
        phone: values.phone || ''
      };

      console.log('Sending to API:', companyData);

      if (editingCompany) {
        const response = await setupAPI.updateCompany(editingCompany.id, companyData);
        message.success('Company updated successfully');
      } else {
        const response = await setupAPI.createCompany(companyData);
        message.success('Company created successfully');
      }
      setModalVisible(false);
      setEditingCompany(null);
      fetchCompanies(); // Refresh the list from server
    } catch (error) {
      message.error(`Failed to ${editingCompany ? 'update' : 'create'} company`);
      console.error('Form submit error:', error);
      console.error('Error response:', error.response?.data);
    } finally {
      setFormLoading(false);
    }
  };





  const breadcrumbs = [
    { title: 'Setup', href: '/setup' },
    { title: 'Company Setup' }
  ];

  const actions = [
    {
      label: 'New Company',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: handleCreate
    }
  ];

  return (
    <>
      <PageHeader
        title="Companies"
        subtitle="Configure your companies and their settings"
        breadcrumbs={breadcrumbs}
        actions={actions}
      />

      <PageContent>
        <Table
          columns={[
            {
              title: 'Company Name',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: 'Email',
              dataIndex: 'email',
              key: 'email',
            },
            {
              title: 'Phone',
              dataIndex: 'phone',
              key: 'phone',
            },
            {
              title: 'Status',
              dataIndex: 'is_active',
              key: 'is_active',
              render: (active) => (
                <Tag color={active ? 'green' : 'red'}>
                  {active ? 'Active' : 'Inactive'}
                </Tag>
              ),
            },
            {
              title: 'Actions',
              key: 'actions',
              render: (_, record) => (
                <Space>
                  <Button
                    type="link"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(record)}
                  >
                    Edit
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={companies}
          loading={loading}
          rowKey="id"
          pagination={false}
        />
      </PageContent>

      <Modal
        title={editingCompany ? 'Edit Company' : 'Create New Company'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        destroyOnHidden
      >
        <CompanyForm
          initialValues={editingCompany}
          onSubmit={handleFormSubmit}
          onCancel={() => setModalVisible(false)}
          loading={formLoading}
        />
      </Modal>
    </>
  );
};

export default CompanySetup;
