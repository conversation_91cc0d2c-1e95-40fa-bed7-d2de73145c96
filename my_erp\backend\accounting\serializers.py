"""
API Serializers for Accounting Module - Based on Odoo's Structure
"""
from rest_framework import serializers
from .models import (
    ResCom<PERSON>y, ResPartner, AccountAccount, AccountGroup, AccountJournal
)


class CompanySerializer(serializers.ModelSerializer):
    """Company serializer - Based on Odoo res.company"""
    class Meta:
        model = ResCompany
        fields = '__all__'


class PartnerSerializer(serializers.ModelSerializer):
    """Partner serializer - Based on Odoo res.partner"""
    is_customer = serializers.BooleanField(read_only=True)
    is_vendor = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ResPartner
        fields = '__all__'


class AccountGroupSerializer(serializers.ModelSerializer):
    """Account Group serializer - Based on Odoo account.group"""
    children = serializers.SerializerMethodField()
    accounts_count = serializers.SerializerMethodField()

    class Meta:
        model = AccountGroup
        fields = ['id', 'name', 'code_prefix_start', 'code_prefix_end', 'parent_id',
                 'level', 'complete_name', 'company_id', 'children', 'accounts_count']

    def get_children(self, obj):
        children = obj.child_groups.all()
        return AccountGroupSerializer(children, many=True, context=self.context).data

    def get_accounts_count(self, obj):
        return obj.accounts.count()


class AccountSerializer(serializers.ModelSerializer):
    """Account serializer - Based on Odoo account.account"""
    balance = serializers.DecimalField(max_digits=16, decimal_places=2, read_only=True)
    group_name = serializers.CharField(source='group_id.name', read_only=True)
    parent_name = serializers.CharField(source='parent_id.name', read_only=True)
    children = serializers.SerializerMethodField()

    class Meta:
        model = AccountAccount
        fields = ['id', 'name', 'code', 'account_type', 'group_id', 'group_name',
                 'parent_id', 'parent_name', 'reconcile', 'deprecated', 'currency_id',
                 'note', 'company_id', 'balance', 'children']

    def get_children(self, obj):
        children = obj.child_accounts.all()
        return AccountSerializer(children, many=True, context=self.context).data


class JournalSerializer(serializers.ModelSerializer):
    """Journal serializer - Based on Odoo account.journal"""
    default_account_name = serializers.CharField(source='default_account_id.name', read_only=True)
    
    class Meta:
        model = AccountJournal
        fields = '__all__'
