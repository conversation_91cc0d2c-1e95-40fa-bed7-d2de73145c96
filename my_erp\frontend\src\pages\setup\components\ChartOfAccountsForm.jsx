/**
 * Chart of Accounts Form Component - Create/Edit Accounts and Account Groups
 */
import React, { useEffect } from 'react';
import { Form, Row, Col, Alert } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import { 
  TextField, 
  TextAreaField, 
  SelectField, 
  SwitchField
} from '../../../components/shared/forms/FormField';

const ChartOfAccountsForm = ({ 
  initialValues, 
  type = 'account', // 'account' or 'group'
  accountTypes = [],
  accountGroups = [],
  accounts = [],
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    } else {
      // Set default values
      if (type === 'account') {
        form.setFieldsValue({
          reconcile: false,
          deprecated: false,
          account_type: 'asset_current'
        });
      } else {
        form.setFieldsValue({
          level: 0
        });
      }
    }
  }, [initialValues, type, form]);

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  // Get parent group options (exclude self and children for groups)
  const getParentGroupOptions = () => {
    if (type !== 'group') return accountGroups;
    
    if (!initialValues) return accountGroups;
    
    // For editing, exclude self and children
    const excludeIds = [initialValues.id];
    const addChildrenIds = (groupId) => {
      accountGroups.forEach(group => {
        if (group.parent_id === groupId && !excludeIds.includes(group.id)) {
          excludeIds.push(group.id);
          addChildrenIds(group.id);
        }
      });
    };
    addChildrenIds(initialValues.id);
    
    return accountGroups.filter(group => !excludeIds.includes(group.id));
  };

  // Get parent account options (exclude self and children for accounts)
  const getParentAccountOptions = () => {
    if (type !== 'account') return accounts;
    
    if (!initialValues) return accounts;
    
    // For editing, exclude self and children
    const excludeIds = [initialValues.id];
    const addChildrenIds = (accountId) => {
      accounts.forEach(account => {
        if (account.parent_id === accountId && !excludeIds.includes(account.id)) {
          excludeIds.push(account.id);
          addChildrenIds(account.id);
        }
      });
    };
    addChildrenIds(initialValues.id);
    
    return accounts.filter(account => !excludeIds.includes(account.id));
  };

  const getFormTitle = () => {
    const itemType = type === 'account' ? 'Account' : 'Account Group';
    const action = initialValues ? 'Edit' : 'Create';
    return `${action} ${itemType}`;
  };

  const getFormDescription = () => {
    if (type === 'account') {
      return 'Accounts are the individual ledger accounts used to record transactions. They can be organized into groups for better structure.';
    } else {
      return 'Account groups help organize your chart of accounts into a hierarchical structure, making it easier to manage and report on your accounts.';
    }
  };

  if (type === 'group') {
    return (
      <FormWrapper
        title={null}
        form={form}
        onFinish={handleSubmit}
        onCancel={onCancel}
        loading={loading}
        submitText={getFormTitle()}
        showReset={true}
      >
        <Alert
          message="Account Group Configuration"
          description={getFormDescription()}
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        {/* Basic Information */}
        <Row gutter={16}>
          <Col span={24}>
            <TextField
              name="name"
              label="Group Name"
              required
              placeholder="Enter group name (e.g., Current Assets)"
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <TextField
              name="code_prefix_start"
              label="Code Prefix Start"
              placeholder="e.g., 1000"
              help="Starting code range for accounts in this group"
            />
          </Col>
          <Col span={12}>
            <TextField
              name="code_prefix_end"
              label="Code Prefix End"
              placeholder="e.g., 1999"
              help="Ending code range for accounts in this group"
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <SelectField
              name="parent_id"
              label="Parent Group"
              options={getParentGroupOptions().map(group => ({
                value: group.id,
                label: group.complete_name || group.name
              }))}
              placeholder="Select parent group (optional)"
              allowClear
            />
          </Col>
        </Row>
      </FormWrapper>
    );
  }

  return (
    <FormWrapper
      title={null}
      form={form}
      onFinish={handleSubmit}
      onCancel={onCancel}
      loading={loading}
      submitText={getFormTitle()}
      showReset={true}
    >
      <Alert
        message="Account Configuration"
        description={getFormDescription()}
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="code"
            label="Account Code"
            required
            placeholder="e.g., 1001"
          />
        </Col>
        <Col span={12}>
          <TextField
            name="name"
            label="Account Name"
            required
            placeholder="e.g., Cash in Hand"
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <SelectField
            name="account_type"
            label="Account Type"
            required
            options={accountTypes.map(type => ({
              value: type.value,
              label: type.label
            }))}
            placeholder="Select account type"
          />
        </Col>
        <Col span={12}>
          <SelectField
            name="group_id"
            label="Account Group"
            options={accountGroups.map(group => ({
              value: group.id,
              label: group.complete_name || group.name
            }))}
            placeholder="Select account group (optional)"
            allowClear
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <SelectField
            name="parent_id"
            label="Parent Account"
            options={getParentAccountOptions().map(account => ({
              value: account.id,
              label: `${account.code} - ${account.name}`
            }))}
            placeholder="Select parent account (optional)"
            allowClear
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="currency_id"
            label="Currency"
            placeholder="e.g., USD, EUR (optional)"
            help="Forces all transactions to use this currency"
          />
        </Col>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="reconcile"
              label="Allow Reconciliation"
              checkedChildren="Yes"
              unCheckedChildren="No"
            />
          </div>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="deprecated"
              label="Deprecated"
              checkedChildren="Yes"
              unCheckedChildren="No"
            />
          </div>
        </Col>
      </Row>

      {/* Notes */}
      <Row gutter={16}>
        <Col span={24}>
          <TextAreaField
            name="note"
            label="Internal Notes"
            placeholder="Enter internal notes about this account"
            rows={3}
          />
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default ChartOfAccountsForm;
