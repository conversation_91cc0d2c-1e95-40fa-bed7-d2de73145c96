/**
 * Sales Tax Form Component - Create/Edit Sales Tax Configuration
 */
import React, { useEffect } from 'react';
import { Form, Row, Col, Alert } from 'antd';
import FormWrapper from '../../../components/shared/forms/FormWrapper';
import {
  TextField,
  SelectField,
  SwitchField,
  PercentageField
} from '../../../components/shared/forms/FormField';

const SalesTaxForm = ({
  initialValues,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    } else {
      // Set default values for new tax
      form.setFieldsValue({
        is_active: true,
        is_default: false,
        tax_type: 'both'
      });
    }
  }, [initialValues, form]);

  const handleSubmit = (values) => {
    onSubmit(values);
  };

  // Tax type options based on TaxConfiguration model
  const taxTypeOptions = [
    { value: 'sales', label: 'Sales Tax' },
    { value: 'purchase', label: 'Purchase Tax' },
    { value: 'both', label: 'Both Sales & Purchase' },
  ];

  return (
    <FormWrapper
      title={null}
      form={form}
      onFinish={handleSubmit}
      onCancel={onCancel}
      loading={loading}
      submitText={initialValues ? 'Update Sales Tax' : 'Create Sales Tax'}
      showReset={true}
    >
      <Alert
        message="Sales Tax Configuration"
        description="Configure sales tax rates that apply to sales transactions and purchase transactions."
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* Basic Information */}
      <Row gutter={16}>
        <Col span={12}>
          <TextField
            name="name"
            label="Tax Name"
            required
            placeholder="Enter tax name (e.g., VAT Standard Rate)"
          />
        </Col>
        <Col span={12}>
          <PercentageField
            name="rate"
            label="Tax Rate"
            required
            placeholder="Enter tax rate"
            min={0}
            max={100}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <SelectField
            name="tax_type"
            label="Tax Type"
            required
            options={taxTypeOptions}
            placeholder="Select tax type"
          />
        </Col>
      </Row>

      {/* Status and Options */}
      <Row gutter={16}>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="is_active"
              label="Active"
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ paddingTop: '30px' }}>
            <SwitchField
              name="is_default"
              label="Default Tax"
              checkedChildren="Default"
              unCheckedChildren="Optional"
            />
          </div>
        </Col>
      </Row>
    </FormWrapper>
  );
};

export default SalesTaxForm;
